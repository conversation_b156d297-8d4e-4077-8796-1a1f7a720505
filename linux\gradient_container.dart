import 'package:flutter/material.dart';

class GradientContainer extends StatefulWidget {
  GradientContainer({required this.colors,super.key});
  List<Color> colors;

  @override
  State<GradientContainer> createState() => _GradientContainerState();
}

class _GradientContainerState extends State<GradientContainer> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(gradient: LinearGradient(colors: widget.colors)),
    );
  }
}